<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Services\School\Assessment\BatchDownloadPdfService;
use App\Services\Tool\MessageService;
use App\Models\School\Assessment\AssessmentBatchDownloadRecord;
use Throwable;

class BatchDownloadPdfJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务尝试次数
     *
     * @var int
     */
    public $tries = 3;

    /**
     * 任务参数
     *
     * @var array
     */
    protected $params;

    /**
     * Create a new job instance.
     *
     * @param array $params 请求参数
     * @param int $recordId 下载记录ID
     * @param string $recordName 记录名称
     * @param int $userId 用户ID
     * @return void
     */
    public function __construct(array $params)
    {
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(BatchDownloadPdfService $batchDownloadPdfService)
    {
        try {
            Log::info('开始批量生成PDF压缩包', ['params' => $this->params, 'record_id' => $this->params['record_id']]);

            // 获取按班级分组的PDF数据
            $classPdfGroups = $batchDownloadPdfService->getPdfUrls($this->params);

            // 创建一个统一的ZIP文件
            $zip_url = $batchDownloadPdfService->createUnifiedZipArchive($classPdfGroups, $this->params['record_name']);
            
            // 更新下载记录状态
            $this->updateDownloadRecord($this->params['record_id'], $zip_url);

            // 发送消息通知
            $this->sendNotification($zip_url);

            Log::info('PDF压缩包已生成', [
                'record_id' => $this->params['record_id'],
                'zip_url' => $zip_url
            ]);
        } catch (\Exception $e) {
            Log::error('PDF压缩包生成失败', [
                'record_id' => $this->params['record_id'],
                'error' => $e->getMessage()
            ]);
            
            // 发送失败通知
            $this->sendFailureNotification($e->getMessage());
            
            throw new \Exception("PDF批量下载失败: {$e->getMessage()}", 500, $e);
        }
    }

    /**
     * 更新下载记录状态
     *
     * @param int $recordId
     * @param string $zip_url
     * @return void
     */
    protected function updateDownloadRecord(int $record_id, string $zip_url): void
    {
        AssessmentBatchDownloadRecord::where('id', $record_id)
            ->update([
                'zip_url' => $zip_url,
                'is_completed' => true
            ]);
    }

    /**
     * 发送成功通知
     *
     * @param string $zip_url
     * @return void
     */
    protected function sendNotification(string $zip_url): void
    {
        $message = [
            'title' => 'PDF批量下载成功',
            'content' => '测评任务：' . $this->params['record_name'] . ' PDF批量下载成功',
            'type' => 'success',
            'url' => $zip_url,
            'url_type' => 1
        ];
        
        $messageService = new MessageService();
        $messageService->sendMessage($this->params['user_id'], $message);
    }

    /**
     * 发送失败通知
     *
     * @param string $error_message
     * @return void
     */
    protected function sendFailureNotification(string $error_message): void
    {
        $message = [
            'title' => 'PDF批量下载失败',
            'content' => '测评任务：' . $this->params['record_name'] . ' PDF批量下载失败，原因：' . $error_message,
            'type' => 'error'
        ];
        
        $messageService = new MessageService();
        $messageService->sendMessage($this->params['user_id'], $message);
    }

    /**
     * 处理失败作业
     */
    public function failed(Throwable $exception): void
    {
        Log::error('PDF批量下载队列任务失败', [
            'record_id' => $this->params['record_id'],
            'error' => $exception->getMessage()
        ]);
        
        // 更新记录状态为失败
        AssessmentBatchDownloadRecord::where('id', $this->params['record_id'])
            ->update([
                'is_completed' => false
            ]);
    }
}