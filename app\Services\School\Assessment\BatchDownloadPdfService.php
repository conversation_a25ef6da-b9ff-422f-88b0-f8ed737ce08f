<?php
namespace App\Services\School\Assessment;

use Illuminate\Support\Facades\Storage;
use App\Services\CreatePdfFileService;
use Illuminate\Support\Collection;
use App\Services\BaseService;
use App\Models\School\Assessment\AssessmentTaskAssignment;
use App\Models\School\Assessment\Assessment;
use App\Models\School\Assessment\AssessmentSchedule;
use App\Models\School\Assessment\AssessmentBatchDownloadRecord;
use ZipArchive;
use Exception;

class BatchDownloadPdfService extends BaseService
{
    protected PdfGeneratorService $pdfGeneratorService;

    public function __construct(PdfGeneratorService $pdfGeneratorService)
    {
        $this->pdfGeneratorService = $pdfGeneratorService;
    }

    /**
     * 处理批量下载PDF请求
     *
     * @param array $params 请求参数
     * @return array 各班级下载信息
     * @throws Exception
     */
    public function handle(array $params): array
    {
        // 录入批量下载记录表
        $recordName = $this->getRecordName($params);
        $recordId = $this->createDownloadRecord($params, $recordName);

        // 获取按班级分组的PDF数据
        $classPdfGroups = $this->getPdfUrls($params);

        // 创建一个统一的ZIP文件
        $zipUrl = $this->createUnifiedZipArchive($classPdfGroups, $recordName);
        
        // 更新下载记录状态
        $this->updateDownloadRecord($recordId, $zipUrl);

        return [$recordName, $zipUrl];
    }

    /**
     * 创建统一的ZIP归档文件，包含所有班级的PDF
     *
     * @param array $classPdfGroups
     * @param string $recordName
     * @return string ZIP文件URL
     * @throws Exception
     */
    public function createUnifiedZipArchive(array $classPdfGroups, string $recordName): string
    {
        $tempDir = storage_path('app/temp/pdf_zip');
        $zipFileName = $this->sanitizeFileName($recordName) . '_' . date('YmdHis') . '.zip';
        $localZipPath = $tempDir . '/' . $zipFileName;

        $this->ensureTempDirectoryExists($tempDir);
        
        $zip = new ZipArchive();
        if ($zip->open($localZipPath, ZipArchive::CREATE) !== TRUE) {
            throw new Exception('无法创建ZIP文件');
        }

        $addedFiles = 0;
        
        // 为每个班级创建一个文件夹
        foreach ($classPdfGroups as $group) {
            $className = ($group['school_year'] ? $group['school_year'] . '_' : '') . $group['class_name'];
            $folderName = $this->sanitizeFileName($className);
            
            // 添加班级文件夹下的PDF文件
            $classAddedFiles = $this->addClassFilesToZip($zip, $group['files'], $tempDir, $folderName);
            $addedFiles += $classAddedFiles;
        }
        
        if ($addedFiles === 0) {
            throw new Exception('没有可用的PDF文件可以打包');
        }

        $zip->close();

        $remoteZipPath = $this->uploadZipToSftp($localZipPath, $zipFileName);
        
        // 清理临时文件
        $this->cleanupTempFiles($tempDir, $localZipPath);
        
        return 'https://s.yishengya.cn' . $remoteZipPath;
    }

    /**
     * 添加班级文件到ZIP
     *
     * @param ZipArchive $zip
     * @param array $pdfUrls
     * @param string $tempDir
     * @param string $folderName 班级文件夹名称
     * @return int 添加的文件数量
     */
    protected function addClassFilesToZip(ZipArchive $zip, array $pdfUrls, string $tempDir, string $folderName): int
    {
        $addedFiles = 0;
        
        foreach ($pdfUrls as $pdfInfo) {
            $remotePath = parse_url($pdfInfo['url'], PHP_URL_PATH);
            if (empty($remotePath)) {
                continue; // 跳过无效URL
            }
            
            $localPdfPath = $tempDir . '/' . basename($remotePath);
            
            // 从SFTP下载PDF
            $fileContent = Storage::disk('sftp_assessment')->get($remotePath);
            if (empty($fileContent)) {
                continue; // 跳过不存在的文件
            }
            
            // 保存到本地临时文件
            file_put_contents($localPdfPath, $fileContent);
            
            // 生成文件名并添加到ZIP的班级文件夹中
            $filename = $this->generatePdfFileName($pdfInfo);
            $zipPath = $folderName . '/' . $filename;
            
            // linux方法1：老写法linux可能会用到，windows上会报乱码，暂时不要删
            // $zip->addFile($localPdfPath, iconv('UTF-8', 'GBK//IGNORE', $zipPath));

            // 修复中文文件名乱码问题
            // Windows方法1：
            $zip->setArchiveComment('UTF-8');
            $zip->addFile($localPdfPath, $zipPath);
            
            $addedFiles++;
        }
        
        return $addedFiles;
    }

    /**
     * 获取记录名称
     *
     * @param array $params
     * @return string
     */
    protected function getRecordName(array $params): string
    {
        $scheduleName = AssessmentSchedule::where('id', $params['assessment_schedule_id'])
            ->value('name') ?? '';
            
        $assessmentName = Assessment::where('id', $params['assessment_id'])
            ->value('name') ?? '';
            
        return $scheduleName . $assessmentName;
    }

    /**
     * 创建下载记录
     *
     * @param array $params
     * @param string $zipName
     * @return int 记录ID
     */
    protected function createDownloadRecord(array $params, string $zipName): int
    {
        $detail = [
            'assessment_schedule_id' => $params['assessment_schedule_id'],
            'assessment_task_id' => $params['assessment_task_id'],
            'assessment_id' => $params['assessment_id'],
        ];
        
        $record = AssessmentBatchDownloadRecord::create([
            'name' => $zipName,
            'detail' => $detail,
            'creator' => $params['creator'],
            'created_at' => now(),
        ]);
        
        return $record->id;
    }

    /**
     * 更新下载记录状态
     *
     * @param int $recordId
     * @param string $zipUrl
     * @return void
     */
    public function updateDownloadRecord(int $recordId, string $zipUrl): void
    {
        AssessmentBatchDownloadRecord::where('id', $recordId)
            ->update([
                'zip_url' => $zipUrl,
                'is_completed' => true
            ]);
    }

    /**
     * 获取按班级分组的PDF URL数据
     *
     * @param array $params
     * @return array
     * @throws Exception
     */
    public function getPdfUrls(array $params): array
    {
        // 将pdf_url为null的补全
        $this->complete_pdf_url($params);

        $query = AssessmentTaskAssignment::with(['student', 'assessment'])
            ->join('student_classes', 'assessment_task_assignments.student_class_id', '=', 'student_classes.id')
            ->join('classes', 'student_classes.class_id', '=', 'classes.id')
            ->select(
                'assessment_task_assignments.*', 
                'student_classes.class_id',
                'student_classes.school_year',
                'classes.class_name'
            )
            ->where([
                'assessment_task_id' => $params['assessment_task_id'],
                'assessment_task_assignments.school_id' => $params['school_id'],
            ])
            ->whereNotNull('pdf_url');

        if (!$query->exists()) {
            throw new Exception('未找到需要下载的PDF文件');
        }

        $pdfData = $query->get()->groupBy('class_id');

        return $this->formatClassData($pdfData);
    }

    /**
     * 将pdf_url为null的补全
     *
     * @param array $params
     * @return void
     */
    public function complete_pdf_url($params)
    {
        // 将pdf_url为null的补全
        $assignments = AssessmentTaskAssignment::select('id', 'assessment_id')
            ->where('assessment_task_id', $params['assessment_task_id'])
            ->where('school_id', $params['school_id'])
            ->whereNull('pdf_url')
            ->whereNotNull('standard_results')
            ->get();
        
        foreach ($assignments as $assignment) {
            $this->pdfGeneratorService->generatePdf(
                $assignment->assessment_id,
                $assignment->id
            );
        }
    }

    /**
     * 调用CreatePdfFileService服务生成PDF
     *
     * @param PdfZipRequest $request
     * @return string pdf_url
     */
    public function generatePdf($assessment_id, $assessment_task_assignment_id, $path, $frontend_url)
    {
        $html_url = $frontend_url . '/survey/survey_report/' . $path . '?type=pdf&assessment_id=' . $assessment_id . '&assessment_task_assignment_id=' . $assessment_task_assignment_id;

        $pdfFileName = '/assessment/pdf/' . date('Ymd') . '/' . $assessment_task_assignment_id . '-' . $assessment_id . '-' . time() . '.pdf';
        
        //$customDictConfig不同的测评会有不同的配置，所以写在此处
        $customDictConfig = [
            'path' => '/data/ysy/uploads_cdn' . $pdfFileName,
            'width' => '1000px',
            'height' => '1415px',
            'printBackground' => 'true',
        ];
        
        $service = new CreatePdfFileService($html_url);
        $pdf_url = $service->createPdfFile($pdfFileName, $customDictConfig);

        return $pdf_url;
    }

    /**
     * 格式化班级数据
     *
     * @param Collection $pdfData
     * @return array
     */
    protected function formatClassData(Collection $pdfData): array
    {
        return $pdfData->map(function ($classAssignments) {
            $first = $classAssignments->first();
            return [
                'class_id' => $first->class_id,
                'class_name' => $first->class_name ?? '未知班级',
                'school_year' => $first->school_year ?? '',
                'files' => $classAssignments->map(function ($assignment) {
                    return [
                        'url' => $assignment->pdf_url,
                        'student_name' => $assignment->student->student_name ?? '未知学生',
                        'assessment_name' => $assignment->assessment->name ?? '未知测评'
                    ];
                })->toArray()
            ];
        })->values()->toArray();
    }
    
    /**
     * 清理文件名中的非法字符
     *
     * @param string $fileName
     * @return string
     */
    protected function sanitizeFileName(string $fileName): string
    {
        // 移除文件名中的非法字符
        $fileName = preg_replace('/[\/\\\:*?"<>|]/', '_', $fileName);
        // 限制文件名长度
        return mb_substr($fileName, 0, 100);
    }

    /**
     * 确保临时目录存在
     *
     * @param string $tempDir
     * @throws Exception
     */
    protected function ensureTempDirectoryExists(string $tempDir): void
    {
        if (!file_exists($tempDir) && !mkdir($tempDir, 0777, true)) {
            throw new Exception('无法创建临时目录: ' . $tempDir);
        }
    }

    /**
     * 生成PDF文件名
     *
     * @param array $pdfInfo
     * @return string
     */
    protected function generatePdfFileName(array $pdfInfo): string
    {
        $studentName = $this->sanitizeFileName($pdfInfo['student_name']);
        $assessmentName = $this->sanitizeFileName($pdfInfo['assessment_name']);
        
        return sprintf('%s_%s.pdf', $studentName, $assessmentName);
    }

    /**
     * 上传ZIP到SFTP
     *
     * @param string $localZipPath
     * @param string $zipFileName
     * @return string 远程路径
     * @throws Exception
     */
    protected function uploadZipToSftp(string $localZipPath, string $zipFileName): string
    {
        $remoteZipPath = '/saas_upload/assessment/pdf_zip/' . $zipFileName;
        
        $zipContent = file_get_contents($localZipPath);
        if (empty($zipContent)) {
            throw new Exception('ZIP文件生成失败或为空');
        }
        
        Storage::disk('sftp_assessment')->put($remoteZipPath, $zipContent);
        
        return $remoteZipPath;
    }

    /**
     * 清理临时文件
     *
     * @param string $tempDir
     * @param string $localZipPath
     */
    protected function cleanupTempFiles(string $tempDir, string $localZipPath): void
    {
        if (file_exists($tempDir)) {
            array_map('unlink', glob($tempDir . '/*.pdf'));
            if (file_exists($localZipPath)) {
                unlink($localZipPath);
            }
        }
    }
}